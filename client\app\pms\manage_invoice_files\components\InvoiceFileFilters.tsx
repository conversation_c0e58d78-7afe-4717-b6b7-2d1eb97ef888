"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { CalendarIcon, Search, X, Filter } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@radix-ui/react-collapsible"

interface FilterParams {
  search?: string
  dateFrom?: string
  dateTo?: string
  assignedTo?: number[]
  status?: "active" | "deleted" | "all"
}

interface InvoiceFileFiltersProps {
  onFilterChange: (filters: FilterParams) => void
}

export function InvoiceFileFilters({ onFilterChange }: InvoiceFileFiltersProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [search, setSearch] = useState("")
  const [dateFrom, setDateFrom] = useState<Date>()
  const [dateTo, setDateTo] = useState<Date>()
  const [assignedTo, setAssignedTo] = useState<number[]>([])
  const [status, setStatus] = useState<"active" | "deleted" | "all">("all")

  // Apply filters when values change
  useEffect(() => {
    const filters: FilterParams = {}

    if (search.trim()) filters.search = search.trim()
    if (dateFrom) filters.dateFrom = format(dateFrom, "yyyy-MM-dd")
    if (dateTo) filters.dateTo = format(dateTo, "yyyy-MM-dd")
    if (assignedTo.length > 0) filters.assignedTo = assignedTo
    if (status !== "all") filters.status = status

    onFilterChange(filters)
  }, [search, dateFrom, dateTo, assignedTo, status, onFilterChange])

  const clearFilters = () => {
    setSearch("")
    setDateFrom(undefined)
    setDateTo(undefined)
    setAssignedTo([])
    setStatus("all")
  }

  const activeFiltersCount = [search.trim(), dateFrom, dateTo, assignedTo.length > 0, status !== "all"].filter(
    Boolean,
  ).length

  const toggleUserAssignment = (userId: number) => {
    setAssignedTo((prev) => (prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]))
  }

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="pb-3">
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="flex items-center justify-between p-0 h-auto">
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-4 w-4" />
                <span>Filters</span>
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {activeFiltersCount}
                  </Badge>
                )}
              </CardTitle>
            </Button>
          </CollapsibleTrigger>
        </CardHeader>

        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="space-y-2">
              <Label htmlFor="search">Search File Names</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by file name..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date From */}
              <div className="space-y-2">
                <Label>Date From</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn("w-full justify-start text-left font-normal", !dateFrom && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFrom ? format(dateFrom, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={dateFrom} onSelect={setDateFrom} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Date To */}
              <div className="space-y-2">
                <Label>Date To</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn("w-full justify-start text-left font-normal", !dateTo && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateTo ? format(dateTo, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={dateTo} onSelect={setDateTo} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={status} onValueChange={(value: "active" | "deleted" | "all") => setStatus(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Files</SelectItem>
                    <SelectItem value="active">Active Only</SelectItem>
                    <SelectItem value="deleted">Deleted Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Clear Filters */}
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="w-full bg-transparent"
                  disabled={activeFiltersCount === 0}
                >
                  <X className="mr-2 h-4 w-4" />
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Assigned Users */}
            {/* {users && users.length > 0 && (
              <div className="space-y-2">
                <Label>Assigned Users</Label>
                <div className="flex flex-wrap gap-2">
                  {users.map((user) => (
                    <Button
                      key={user.id}
                      variant={assignedTo.includes(user.id) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleUserAssignment(user.id)}
                    >
                      {user.firstName} {user.lastName}
                    </Button>
                  ))}
                </div>
              </div>
            )} */}

            {/* Active Filters Summary */}
            {activeFiltersCount > 0 && (
              <div className="pt-2 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {activeFiltersCount} filter{activeFiltersCount !== 1 ? "s" : ""} applied
                  </span>
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    Clear all
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
