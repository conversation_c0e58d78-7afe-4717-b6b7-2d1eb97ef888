"use client";

import { invoiceFile_routes } from "@/lib/routePath";
import { toast } from "sonner";
import { formSubmit } from "@/lib/helpers";
import { FilterParams } from "../types";

export interface CreateInvoiceFilePayload {
  carrier: number;
  date: string;
  fileName: string;
  noOfPages: number;
  assignedTo?: number;
  createdBy: number;
  updatedBy?: number;
}

export const createInvoiceFile = async (payload: CreateInvoiceFilePayload) => {
  try {
    const result = await formSubmit(
      invoiceFile_routes.CREATE_INVOICE_FILE,
      "POST",
      payload
    );
    if (result.success) {
      toast.success("Invoice file created successfully");
    } else {
      toast.error(result.message || "Error creating invoice file");
    }
    return result;
  } catch (error: any) {
    toast.error(error?.message || "Error creating invoice file");
    throw error;
  }
};

const buildQueryString = (filters: FilterParams = {}): string => {
  const params = new URLSearchParams();

  if (filters.search) params.append("search", filters.search);
  if (filters.dateFrom) params.append("dateFrom", filters.dateFrom.toString());
  if (filters.date) params.append("date", filters.date);
  if (filters.carrier) params.append("carrier", filters.carrier.toString());
  if (filters.assignedTo && filters.assignedTo.toString().length > 0) {
    filters.assignedTo.forEach((id) =>
      params.append("assignedTo", id.toString())
    );
  }

  return params.toString();
};

// GET all invoice files with filters
export const getAllInvoiceFiles = async (
  page = 1,
  limit = 50,
  filters: FilterParams = {}
) => {
  try {
    const query = buildQueryString(filters);
    const url = `${invoiceFile_routes.GET_ALL_INVOICE_FILES}?page=${page}&limit=${limit}&${query}`;
    const result = await formSubmit(url, "GET", undefined);

    if (!result.success) {
      toast.error(result.message || "Error fetching invoice files");
    }
    return result;
  } catch (error: any) {
    toast.error(error?.message || "Error fetching invoice files");
    throw error;
  }
};

export const getInvoiceFilesByUser = async () => {
  try {
    const result = await formSubmit(
      invoiceFile_routes.GET_INVOICE_FILES_BY_USER,
      "GET",
      {}
    );
    if (!result.success) {
      toast.error(result.message || "Error fetching user invoice files");
    }
    return result;
  } catch (error: any) {
    toast.error(error?.message || "Error fetching user invoice files");
    throw error;
  }
};

export const getInvoiceFileById = async (id: string) => {
  try {
    const result = await formSubmit(
      invoiceFile_routes.GET_INVOICE_FILE_BY_ID(id),
      "GET",
      {}
    );
    if (!result.success) {
      toast.error(result.message || "Error fetching invoice file");
    }
    return result;
  } catch (error: any) {
    toast.error(error?.message || "Error fetching invoice file");
    throw error;
  }
};

export const updateInvoiceFile = async (
  id: string,
  payload: Partial<CreateInvoiceFilePayload>
) => {
  try {
    console.log("➡️ Calling updateInvoiceFile with:", { id, payload });
    const url = invoiceFile_routes.UPDATE_INVOICE_FILE(id);
    console.log("➡️ Final URL:", url);

    const result = await formSubmit(url, "PUT", payload);

    console.log("✅ Response from formSubmit:", result);

    if (result.success) {
      toast.success("Invoice file updated successfully");
    } else {
      toast.error(result.message || "Error updating invoice file");
    }
    return result;
  } catch (error: any) {
    console.error("❌ Error in updateInvoiceFile:", error);
    toast.error(error?.message || "Error updating invoice file");
    throw error;
  }
};

export const deleteInvoiceFile = async (id: string) => {
  try {
    const result = await formSubmit(
      invoiceFile_routes.DELETE_INVOICE_FILE(id),
      "DELETE",
      {}
    );
    if (result.success) {
      toast.success("Invoice file deleted successfully");
    } else {
      toast.error(result.message || "Error deleting invoice file");
    }
    return result;
  } catch (error: any) {
    toast.error(error?.message || "Error deleting invoice file");
    throw error;
  }
};

export const bulkAssignInvoiceFiles = async (payload: any) => {
  try {
    const result = await formSubmit(
      invoiceFile_routes.BULK_ASSIGN,
      "POST",
      payload
    );
    if (result.success) {
      toast.success("Invoice files assigned successfully");
    } else {
      toast.error(result.message || "Error assigning invoice files");
    }
    return result;
  } catch (error: any) {
    toast.error(error?.message || "Error assigning invoice files");
    throw error;
  }
};