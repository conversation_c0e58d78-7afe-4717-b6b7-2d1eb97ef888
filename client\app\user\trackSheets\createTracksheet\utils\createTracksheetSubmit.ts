import { formSubmit } from "@/lib/helpers";
import { trackSheets_routes } from "@/lib/routePath";

interface SubmitProps {
  values: any;
  form: any;
  clientFilePathFormat: string | null;
  generateFilename: (index: number, values: any, clientFilePathFormat: string | null) => {
    filename: string;
    isValid: boolean;
    missing: string[];
  };
  notify: (type: "success" | "error", message: string) => void;
  onSuccess?: () => void;
  userData?: any;
}

// Helper to combine alias/company, address, and zipcode into one string
const combineAddressFields = (alias: string, address: string, zipcode: string): string => {
  const parts: string[] = [];
  if (alias && alias.trim() !== "") parts.push(alias.trim());
  if (address && address.trim() !== "") parts.push(address.trim());
  if (zipcode && zipcode.trim() !== "") parts.push(zipcode.trim());
  return parts.join(", ");
};

export const createTracksheetSubmit = async ({
  values,
  form,
  clientFilePathFormat,
  generateFilename,
  notify,
  onSuccess,
  userData,
}: SubmitProps) => {
  try {
    if (!values.entries?.length) {
      notify("error", "Please add at least one entry.");
      return;
    }

    // Validate and assign filenames for each entry
    const generatedFilenames: string[] = [];
    for (let i = 0; i < values.entries.length; i++) {
      const result = generateFilename(i, values, clientFilePathFormat);
      if (!result.isValid) {
        notify(
          "error",
          `Entry ${i + 1} is missing: ${result.missing.join(", ")}`
        );
        return;
      }
      generatedFilenames[i] = result.filename;
    }

    // Map frontend warning fields to backend fields for each entry
    const freightTermMap: { [key: string]: string } = {
      "Prepaid": "PREPAID",
      "Collect": "COLLECT",
      "Third Party Billing": "THIRD_PARTY",
    };
    const entries = values.entries.map((entry: any, index: number) => ({
      company: entry.company,
      division: entry.division,
      invoice: entry.invoice,
      masterInvoice: entry.masterInvoice,
      bol: entry.bol,
      invoiceDate: entry.invoiceDate,
      receivedDate: entry.receivedDate,
      shipmentDate: entry.shipmentDate,
      carrierId: entry.carrierName,
      invoiceStatus: entry.invoiceStatus,
      manualMatching: entry.manualMatching,
      invoiceType: entry.invoiceType,
      billToClient: entry.billToClient,
      finalInvoice: entry.finalInvoice,
      currency: entry.currency,
      qtyShipped: entry.qtyShipped,
      weightUnitName: entry.weightUnitName,
      quantityBilledText: entry.quantityBilledText,
      freightClass: entry.freightClass,
      invoiceTotal: entry.invoiceTotal,
      savings: entry.savings,
      ftpFileName: entry.ftpFileName,
      filePath: generatedFilenames[index], 
      ftpPage: entry.ftpPage,
      docAvailable: entry.docAvailable,
      notes: entry.notes,
      customFields: entry.customFields?.map((cf: any) => ({
        id: cf.id,
        value: cf.value,
      })),
      enteredBy: values.enteredBy || entry.enteredBy || "",
      // --- Map warning fields ---
      freightTerm: freightTermMap[entry.legrandFreightTerms] || undefined,
      shipperAddressType: entry.shipperType,
      consigneeAddressType: entry.consigneeType,
      billToAddressType: entry.billtoType,
      // Combine alias/company, address, and zipcode into single address fields
      shipperAddress: combineAddressFields(entry.shipperAlias, entry.shipperAddress, entry.shipperZipcode),
      consigneeAddress: combineAddressFields(entry.consigneeAlias, entry.consigneeAddress, entry.consigneeZipcode),
      billToAddress: combineAddressFields(entry.billtoAlias, entry.billtoAddress, entry.billtoZipcode),
    }));
    const formData = {
      clientId: values.clientId,
      entries: entries,
    };

    // Use the old formSubmit helper
    const result = await formSubmit(
      trackSheets_routes.CREATE_TRACK_SHEETS,
      "POST",
      formData
    );
console.log("data", result);
    if (result.success) {
      notify("success", "All TrackSheets created successfully");

      // Save the carrier and received date
      const currentCarrier = values.entries[0]?.carrierName;
      const currentReceivedDate = values.entries[0]?.receivedDate;

      // Reset the form but keep the two fields
      form.reset({
        ...values, // Keep other top-level values like clientId
        entries: [
          {
            company: "",
            division: "",
            invoice: "",
            masterInvoice: "",
            bol: "",
            invoiceDate: "",
            receivedDate: currentReceivedDate, // Keep this value
            shipmentDate: "",
            carrierName: currentCarrier, // Keep this value
            invoiceStatus: "ENTRY",
            manualMatching: "",
            invoiceType: "",
            billToClient: "yes",
            finalInvoice: "",
            currency: "",
            qtyShipped: "",
            weightUnitName: "",
            quantityBilledText: "",
            freightClass: "",
            invoiceTotal: "",
            savings: "",
            financialNotes: "",
            ftpFileName: "",
            ftpPage: "",
            docAvailable: [],
            otherDocuments: "",
            notes: "",
            legrandAlias: "",
            legrandCompanyName: "",
            legrandAddress: "",
            legrandZipcode: "",
            shipperAlias: "",
            shipperAddress: "",
            shipperZipcode: "",
            consigneeAlias: "",
            consigneeAddress: "",
            consigneeZipcode: "",
            billtoAlias: "",
            billtoAddress: "",
            billtoZipcode: "",
            shipperType: "",
            consigneeType: "",
            billtoType: "",
            legrandFreightTerms: "",
            customFields: [],
            enteredBy: userData?.username || "",
            
          },
        ],
      });

      if (onSuccess) {
        onSuccess();
      }
    } else {
      notify("error", result.message || "Failed to create TrackSheets");
    }
  } catch (error) {
    notify("error", "An error occurred while creating the TrackSheets");
  }
};
