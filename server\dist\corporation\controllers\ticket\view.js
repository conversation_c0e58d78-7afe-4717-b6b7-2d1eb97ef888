"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTicketStageChangeLogs = exports.viewTicketById = exports.getCurrentUserTickets = exports.viewTicket = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const helpers_1 = require("../../../utils/helpers");
const workTypeToTableMapping = {
    trackSheets: "track_sheets",
    invoiceFiles: "invoice_files",
};
const viewTicket = async (req, res) => {
    try {
        const data = await prismaClient_1.default.ticket.findMany({
            where: { deletedAt: null },
            include: {
                pipeline: { include: { stages: true } },
                stages: true,
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        // Collect all unique assignedTo values from ticket.stages only
        const allAssignedTo = new Set();
        data.forEach(ticket => {
            if (ticket.stages) {
                ticket.stages.forEach(stage => {
                    if (stage.assignedTo)
                        allAssignedTo.add(stage.assignedTo);
                });
            }
        });
        // Fetch user details for all assignedTo values (by username or id)
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0 ? await prismaClient_1.default.user.findMany({
            where: {
                OR: [
                    { username: { in: assignedToArr } },
                    { id: { in: assignedToArr.filter(v => !isNaN(Number(v))).map(v => Number(v)) } },
                ],
            },
            select: { id: true, username: true, firstName: true, lastName: true, email: true },
        }) : [];
        // Helper to get user by assignedTo value
        const getUser = (assignedTo) => users.find(u => u.username === assignedTo || String(u.id) === String(assignedTo));
        const enhancedData = await Promise.all(data.map(async (ticket) => {
            const table = workTypeToTableMapping[ticket.pipeline.workType];
            // Fetch actual tag objects for the tag IDs
            const tags = ticket.tags.length > 0 ? await prismaClient_1.default.tag.findMany({
                where: {
                    id: {
                        in: ticket.tags,
                    },
                    deletedAt: null,
                },
            }) : [];
            // Enrich ticket stages with assignedUser
            const stages = ticket.stages ? ticket.stages.map(stage => ({
                ...stage,
                assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
            })) : ticket.stages;
            // Do not enrich pipeline.stages (no assignedTo field)
            const pipeline = ticket.pipeline ? {
                ...ticket.pipeline,
                stages: ticket.pipeline.stages,
            } : ticket.pipeline;
            if (table && ticket.workItemId) {
                const query = `select * from ${table} where id = ${ticket.workItemId}`;
                const fetchedRecord = await prismaClient_1.default.$queryRawUnsafe(query);
                return {
                    ...ticket,
                    TicketTracksheet: fetchedRecord[0] || null,
                    tags: tags,
                    pipeline,
                    stages,
                };
            }
            return {
                ...ticket,
                tags: tags,
                pipeline,
                stages,
            };
        }));
        return res.status(200).json({
            data: await Promise.all(enhancedData),
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewTicket = viewTicket;
const getCurrentUserTickets = async (req, res) => {
    try {
        const currentUserId = req.user_id || req.body.userId;
        console.log("Current user ID:", currentUserId);
        // Fetch all tickets with their stages
        const data = await prismaClient_1.default.ticket.findMany({
            where: {
                deletedAt: null,
            },
            include: {
                pipeline: { include: { stages: true } },
                stages: true,
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        // Log ticket info before filtering
        console.log("Tickets fetched from DB:", data.map(t => ({
            id: t.id,
            currentStageId: t.currentStageId,
            stages: t.stages.map(s => ({
                id: s.id,
                pipelineStageId: s.pipelineStageId,
                assignedTo: s.assignedTo
            }))
        })));
        // Filter tickets to only those where the current user is assigned to the current stage
        const filteredData = data.filter(ticket => {
            if (!ticket.currentStageId || !ticket.stages || ticket.stages.length === 0) {
                console.log(`Ticket ${ticket.id} skipped: missing currentStageId or stages`);
                return false;
            }
            // Find the TicketStage with pipelineStageId === currentStageId
            const currentStage = ticket.stages.find(stage => stage.pipelineStageId === ticket.currentStageId);
            if (!currentStage) {
                console.log(`Ticket ${ticket.id} skipped: no stage matches currentStageId`);
                return false;
            }
            if (String(currentStage.assignedTo) !== String(currentUserId)) {
                console.log(`Ticket ${ticket.id} skipped: assignedTo (${currentStage.assignedTo}) !== currentUserId (${currentUserId})`);
                return false;
            }
            console.log(`Ticket ${ticket.id} INCLUDED for user ${currentUserId}`);
            return true;
        });
        console.log("Tickets assigned to user:", filteredData.map(t => t.id));
        // Collect all unique assignedTo values from ticket.stages only (for filtered tickets)
        const allAssignedTo = new Set();
        filteredData.forEach(ticket => {
            if (ticket.stages) {
                ticket.stages.forEach(stage => {
                    if (stage.assignedTo)
                        allAssignedTo.add(stage.assignedTo);
                });
            }
        });
        // Fetch user details for all assignedTo values (by username or id)
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0 ? await prismaClient_1.default.user.findMany({
            where: {
                OR: [
                    { username: { in: assignedToArr } },
                    { id: { in: assignedToArr.filter(v => !isNaN(Number(v))).map(v => Number(v)) } },
                ],
            },
            select: { id: true, username: true, firstName: true, lastName: true, email: true },
        }) : [];
        // Helper to get user by assignedTo value
        const getUser = (assignedTo) => users.find(u => u.username === assignedTo || String(u.id) === String(assignedTo));
        const enhancedData = await Promise.all(filteredData.map(async (ticket) => {
            const table = workTypeToTableMapping[ticket.pipeline.workType];
            // Fetch actual tag objects for the tag IDs
            const tags = ticket.tags.length > 0 ? await prismaClient_1.default.tag.findMany({
                where: {
                    id: {
                        in: ticket.tags,
                    },
                    deletedAt: null,
                },
            }) : [];
            // Enrich ticket stages with assignedUser
            const stages = ticket.stages ? ticket.stages.map(stage => ({
                ...stage,
                assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
            })) : ticket.stages;
            // Do not enrich pipeline.stages (no assignedTo field)
            const pipeline = ticket.pipeline ? {
                ...ticket.pipeline,
                stages: ticket.pipeline.stages,
            } : ticket.pipeline;
            if (table && ticket.workItemId) {
                const query = `select * from ${table} where id = ${ticket.workItemId}`;
                const fetchedRecord = await prismaClient_1.default.$queryRawUnsafe(query);
                return {
                    ...ticket,
                    TicketTracksheet: fetchedRecord[0] || null,
                    tags: tags,
                    pipeline,
                    stages,
                };
            }
            return {
                ...ticket,
                tags: tags,
                pipeline,
                stages,
            };
        }));
        return res.status(200).json({
            data: await Promise.all(enhancedData),
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getCurrentUserTickets = getCurrentUserTickets;
const viewTicketById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "Ticket id is required" });
        }
        const data = await prismaClient_1.default.ticket.findUnique({
            where: { id: id },
            include: {
                pipeline: { include: { stages: true } },
                stages: true,
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
        });
        if (!data) {
            return res.status(404).json({ error: "Ticket not found" });
        }
        // Collect all unique assignedTo values from ticket.stages only
        const allAssignedTo = new Set();
        if (data.stages) {
            data.stages.forEach(stage => {
                if (stage.assignedTo)
                    allAssignedTo.add(stage.assignedTo);
            });
        }
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0 ? await prismaClient_1.default.user.findMany({
            where: {
                OR: [
                    { username: { in: assignedToArr } },
                    { id: { in: assignedToArr.filter(v => !isNaN(Number(v))).map(v => Number(v)) } },
                ],
            },
            select: { id: true, username: true, firstName: true, lastName: true, email: true },
        }) : [];
        const getUser = (assignedTo) => users.find(u => u.username === assignedTo || String(u.id) === String(assignedTo));
        // Fetch actual tag objects for the tag IDs
        const tags = data.tags.length > 0 ? await prismaClient_1.default.tag.findMany({
            where: {
                id: {
                    in: data.tags,
                },
                deletedAt: null,
            },
        }) : [];
        // Enrich ticket stages with assignedUser
        const stages = data.stages ? data.stages.map(stage => ({
            ...stage,
            assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
        })) : data.stages;
        // Do not enrich pipeline.stages (no assignedTo field)
        const pipeline = data.pipeline ? {
            ...data.pipeline,
            stages: data.pipeline.stages,
        } : data.pipeline;
        const table = workTypeToTableMapping[data.pipeline.workType];
        let enhancedData = data;
        if (table && data.workItemId) {
            const query = `select * from ${table} where id = ${data.workItemId}`;
            const fetchedRecord = await prismaClient_1.default.$queryRawUnsafe(query);
            enhancedData = {
                ...data,
                workItem: fetchedRecord[0] || null,
                tags: tags,
                pipeline,
                stages,
            };
        }
        else {
            enhancedData = {
                ...data,
                tags: tags,
                pipeline,
                stages,
            };
        }
        return res.status(200).json({
            data: enhancedData,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewTicketById = viewTicketById;
// Get ticket stage change logs
const getTicketStageChangeLogs = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "Ticket id is required" });
        }
        const logs = await prismaClient_1.default.ticketStageChangeLog.findMany({
            where: {
                ticketId: id,
                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        // Enhance logs with pipeline stage names
        const enhancedLogs = await Promise.all(logs.map(async (log) => {
            let fromStageName = "Unknown Stage";
            let toStageName = "Unknown Stage";
            if (log.fromStage) {
                const fromPipelineStage = await prismaClient_1.default.pipelineStage.findUnique({
                    where: { id: log.fromStage },
                    select: { name: true },
                });
                fromStageName = fromPipelineStage?.name || "Unknown Stage";
            }
            if (log.toStage) {
                const toPipelineStage = await prismaClient_1.default.pipelineStage.findUnique({
                    where: { id: log.toStage },
                    select: { name: true },
                });
                toStageName = toPipelineStage?.name || "Unknown Stage";
            }
            return {
                id: log.id,
                ticketId: log.ticketId,
                fromStage: log.fromStage,
                toStage: log.toStage,
                fromStageName,
                toStageName,
                createdBy: log.createdBy,
                createdAt: log.createdAt,
                updatedAt: log.updatedAt,
                updatedBy: log.updatedBy,
            };
        }));
        return res.status(200).json({
            success: true,
            data: enhancedLogs,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getTicketStageChangeLogs = getTicketStageChangeLogs;
//# sourceMappingURL=view.js.map