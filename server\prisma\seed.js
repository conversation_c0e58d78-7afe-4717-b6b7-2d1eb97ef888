const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcrypt");

const prisma = new PrismaClient();
async function createPermissions() {
  const module = [
   
    // Manage Pipelines
    // { module: "PIPELINE MANAGEMENT", action: "view-pipelines" },
    // { module: "PIPELINE MANAGEMENT", action: "create-pipelines" },
    // { module: "PIPELINE MANAGEMENT", action: "update-pipelines" },
    // { module: "PIPELINE MANAGEMENT", action: "delete-pipelines" },

    // Manage Tickets
    // { module: "TICKET MANAGEMENT", action: "view-tickets" },
    // { module: "TICKET MANAGEMENT", action: "create-tickets" },
    // { module: "TICKET MANAGEMENT", action: "update-tickets" },
    // { module: "TICKET MANAGEMENT", action: "delete-tickets" },

    // Manage Legrand Mapping
    // { module: "LEGRAND MAPPING", action: "view-legrand-mappings" },
    // { module: "LEGRAND MAPPING", action: "create-legrand-mappings" },
    // { module: "LEGRAND MAPPING", action: "update-legrand-mappings" },
    // { module: "LEGRAND MAPPING", action: "delete-legrand-mappings" },

    // Manage Invoice Files
    // { module: "INVOICE FILE MANAGEMENT", action: "view-invoice-files" },
    // { module: "INVOICE FILE MANAGEMENT", action: "create-invoice-files" },
    // { module: "INVOICE FILE MANAGEMENT", action: "update-invoice-files" },
    // { module: "INVOICE FILE MANAGEMENT", action: "delete-invoice-files" },
  ];

  const data = module.map(({ module, action }) => ({
    module,
    action,
  }));

  try {
    await prisma.permissions.createMany({
      data,
    });
  } catch (error) {
    console.error("Error while creating permissions:", error);
  }
}

async function createUserTitles() {
  const titles = [
   
  ];

  try {
    await prisma.userTitle.createMany({
      data: titles,
    });
    console.log("User titles seeded successfully!");
  } catch (error) {
    console.error("Error while creating user titles:", error);
  }
}

async function createVisibilityRules() {


  try {
    await prisma.visibilityRule.createMany({
      data: rules,
    });
    console.log("Visibility rules seeded successfully!");
  } catch (error) {
    console.error("Error while creating visibility rules:", error);
  }
}

async function main() {


  await createPermissions();
  // await createUserTitles();
  // await createVisibilityRules();
}

async function run() {
  try {
    await main();
  } catch (e) {
    console.error(e);
  } finally {
    await prisma.$disconnect();
  }
}

run();
