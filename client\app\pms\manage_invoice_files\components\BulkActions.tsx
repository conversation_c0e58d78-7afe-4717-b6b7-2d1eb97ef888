"use client";

import { useState } from "react";
import { UserPlus, Download, Loader2, Plus } from "lucide-react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { InvoiceFile } from "../types";
import { Alert, AlertDescription } from "@/app/_component/Alert";
import { bulkAssignInvoiceFiles } from "../services/invoiceFileService";
import CreateTicketModal from "@/app/user/trackSheets/ticketing_system/CreateTicket";

interface BulkActionsProps {
  selectedFiles: InvoiceFile[];
  onActionComplete: () => void;
  users: any;
  userData: any;
}

export function BulkActions({
  selectedFiles,
  onActionComplete,
  users,
  userData,
}: BulkActionsProps) {
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);

  const handleBulkAssign = async () => {
    const hasSelectedUser = !!selectedUserId;
    const hasSelectedFiles = selectedFiles.length > 0;
    const hasValidUserDataId = !!userData?.id;

    if (!hasSelectedUser || !hasSelectedFiles || !hasValidUserDataId) {
      toast.error("Please select files and a user to assign");
      return;
    }

    setIsProcessing(true);
    try {
      const fileIds = selectedFiles.map((file) => file.id);
      await bulkAssignInvoiceFiles({
        fileIds,
        assignedTo: Number.parseInt(selectedUserId),
        updatedBy: userData.id,
      });

      console.log(`${selectedFiles.length} file(s) assigned successfully`);
      setShowAssignDialog(false);
      setSelectedUserId("");
      onActionComplete();
    } catch (error) {
      toast.error("Failed to assign files");
    } finally {
      setIsProcessing(false);
    }
  };

  const userOptions = Array.isArray(users?.data)
    ? users.data.map((user: any) => ({
        label: `${user.firstName} ${user.lastName}`,
        value: user.id.toString(),
      }))
    : [];
  const handleExport = () => {
    if (selectedFiles.length === 0) return null;

    const formatDate = (date: string | Date) =>
      new Date(date).toLocaleDateString("en-CA"); // formats as YYYY-MM-DD

    const escapeCSV = (value: string | number | null | undefined) =>
      `"${String(value ?? "").replace(/"/g, '""')}"`;

    const csvContent = [
      "Date,File Name,Pages,Assigned To,Status,Created At",
      ...selectedFiles.map((file) =>
        [
          escapeCSV(formatDate(file.date)),
          escapeCSV(file.fileName),
          escapeCSV(file.noOfPages),
          escapeCSV(
            file.assignedToUser
              ? `${file.assignedToUser.firstName} ${file.assignedToUser.lastName}`
              : "Unassigned"
          ),
          escapeCSV(file.deletedAt ? "Deleted" : "Active"),
          escapeCSV(formatDate(file.createdAt)),
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `invoice-files-${new Date().toLocaleDateString("en-CA")}.csv`
    );
    link.click();

    toast.success("Files exported successfully");
  };

  if (selectedFiles.length === 0) return null;

  return (
    <>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium">
                {selectedFiles.length} file
                {selectedFiles.length !== 1 ? "s" : ""} selected
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAssignDialog(true)}
              >
                <UserPlus className="mr-2 h-4 w-4" />
                Assign
              </Button>

              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsTicketModalOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Ticket
                </Button>
              
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Assign Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Files</DialogTitle>
            <DialogDescription>
              Assign {selectedFiles.length} selected file
              {selectedFiles.length !== 1 ? "s" : ""} to a user.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Select User</label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger className="mt-2">
                  <SelectValue placeholder="Choose a user to assign files to" />
                </SelectTrigger>
                <SelectContent>
                  {userOptions.map((user) => (
                    <SelectItem key={user.value} value={user.value}>
                      {user.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Alert>
              <AlertDescription>
                This will assign all {selectedFiles.length} selected files to
                the chosen user.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAssignDialog(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkAssign}
              disabled={!selectedUserId || isProcessing}
            >
              {isProcessing && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Assign Files
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Ticket Modal Integration */}
      <CreateTicketModal
        isOpen={isTicketModalOpen}
        onClose={() => setIsTicketModalOpen(false)}
        onClearSelection={onActionComplete}
        selectedRows={selectedFiles.map(f => ({ id: f.id, invoice: f.fileName }))}
      />
    </>
  );
}
